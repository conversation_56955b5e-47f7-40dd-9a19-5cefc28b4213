import 'package:dio/dio.dart' hide Headers;
import 'package:lsenglish_admin/models/category_resp/category_resp.dart';
import 'package:lsenglish_admin/models/mother_tongue_resp/mother_tongue_resp.dart';
import 'package:lsenglish_admin/models/page_list_resp.dart';
import 'package:lsenglish_admin/models/promotion_code_resp/promotion_code_resp.dart';
import 'package:lsenglish_admin/models/series_resp/series_resp.dart';
import 'package:lsenglish_admin/models/user_resp.dart';
import 'package:retrofit/retrofit.dart';
import '../models/login_resp/login_resp.dart';
import '../models/resource_resp/resource_resp.dart';
import 'api_result.dart';
part 'client.g.dart';

@RestApi(baseUrl: '/api/v1/admin/')
abstract class RestClient {
  factory RestClient(Dio dio) = _RestClient;

  @POST('user/login')
  Future<ApiResult<LoginResp>> loginAdmin(@Body() Map<String, dynamic> data);

  @GET('users')
  Future<ApiResult<PageListResp<List<UserResp>>>> userList(
    @Query('currentpage') int currentpage,
    @Query('pagesize') int pagesize,
  );

  @DELETE('users')
  Future<void> deleteUserMulti(@Query('ids') List<String> categoryId);

  @GET('categorys')
  Future<ApiResult<PageListResp<List<CategoryResp>>>> categoryList(
    @Query('currentpage') int currentpage,
    @Query('pagesize') int pagesize, {
    @Query('sortType') int sortType = 0,
  });

  @POST('category')
  Future<void> addCategory(@Body() Map<String, dynamic> data);

  @DELETE('category')
  Future<void> deleteCategory(@Query('id') String categoryId);

  @POST('category/priority')
  Future<void> setCategoryPriority(@Body() Map<String, dynamic> data);

  @DELETE('categorys')
  Future<void> deleteCategoryMulti(@Query('ids') List<String> categoryId);

  @GET('resources')
  Future<ApiResult<PageListResp<List<ResourceResp>>>> resourceList(
    @Query('categoryId') String categoryId,
    @Query('currentpage') int currentpage,
    @Query('pagesize') int pagesize,
    @Query('sortType') int sortType,
  );

  @POST('resource')
  Future<void> addResource(@Body() Map<String, dynamic> data);

  @GET('resource')
  Future<ApiResult<ResourceResp>> getResourceDetail(@Query('id') String id);

  @POST('resource/seaturedContent')
  Future<void> setResourceFeaturedContent(@Body() Map<String, dynamic> data);

  @POST('resource/priority')
  Future<void> setResourcePriority(@Body() Map<String, dynamic> data);

  @DELETE('resource')
  Future<void> deleteResource(@Query('id') String categoryId);

  @DELETE('resources')
  Future<void> deleteResourceMulti(@Query('ids') List<String> categoryId);

  @POST('upload')
  @MultiPart()
  @Headers(<String, dynamic>{
    "accept": "*/*",
    "Content-Type": "multipart/form-data",
  })
  Future<ApiResult<List<String>>> uploadFiles({
    @Part() required List<MultipartFile> files,
  });

  @GET('nativeLangs')
  Future<ApiResult<List<NativeLangResp>>> nativeLangList();

  @GET('serieses')
  Future<ApiResult<PageListResp<List<SeriesResp>>>> seriesList(
    @Query('currentpage') int currentpage,
    @Query('pagesize') int pagesize, {
    @Query('sortType') int sortType = 0,
  });

  @POST('series')
  Future<void> addSeries(@Body() Map<String, dynamic> data);

  @POST('series/seaturedContent')
  Future<void> setSeriesFeaturedContent(@Body() Map<String, dynamic> data);

  @POST('series/priority')
  Future<void> setSeriesPriority(@Body() Map<String, dynamic> data);

  @GET('series')
  Future<ApiResult<SeriesResp>> getSeriesDetail(@Query('id') String id);

  @DELETE('series')
  Future<void> deleteSeries(@Query('id') String categoryId);

  @DELETE('serieses')
  Future<void> deleteSeriesMulti(@Query('ids') List<String> categoryId);

  @GET('vip/promotionCodes')
  Future<ApiResult<PageListResp<List<PromotionCodeResp>>>> getPromotionCodes(
    @Query('currentpage') int currentpage,
    @Query('pagesize') int pagesize,
  );

  @POST('vip/promotionCode')
  Future<void> addPromotionCode(@Body() Map<String, dynamic> data);

  @POST('vip/exchangeCode')
  Future<void> exchangeCode(@Body() Map<String, dynamic> data);

  @DELETE('vip/promotionCode')
  Future<void> deletePromotionCode(@Query('id') String id);

  @DELETE('vip/promotionCodes')
  Future<void> deletePromotionCodeMulti(@Query('ids') List<String> ids);

  @GET('resource/tags')
  Future<ApiResult<List<String>>> getResourceTags(@Query('resourceId') String resourceId);

  @POST('resource/tags')
  Future<void> setResourceTags(@Body() Map<String, dynamic> data);

  @POST('resource/tag')
  Future<void> addResourceTag(@Body() Map<String, dynamic> data);

  @DELETE('resource/tag')
  Future<void> removeResourceTag(@Query('resourceId') String resourceId, @Query('tag') String tag);

  @GET('resources/bytags')
  Future<ApiResult<List<ResourceResp>>> getResourcesByTags(
    @Query('tags') List<String> tags,
    @Query('langCode') String langCode,
  );
}
