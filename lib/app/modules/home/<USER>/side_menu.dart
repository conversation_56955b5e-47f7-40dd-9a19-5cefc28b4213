import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lsenglish_admin/app/modules/home/<USER>/home_controller.dart';
import 'package:lsenglish_admin/common/constants.dart';
import 'package:lsenglish_admin/generated/locales.g.dart';

class DrawerItem {
  const DrawerItem(this.label, this.icon, this.selectedIcon);

  final String label;
  final Widget icon;
  final Widget selectedIcon;
}

List<DrawerItem> userDestinations = <DrawerItem>[
  DrawerItem(LocaleKeys.drawer_user_manager.tr, const Icon(Icons.account_circle_outlined), const Icon(Icons.account_circle)),
];

List<DrawerItem> sourceDestinations = <DrawerItem>[
  const DrawerItem("分类管理", Icon(Icons.category_outlined), Icon(Icons.category)),
  const DrawerItem("剧集管理", Icon(Icons.movie_creation_outlined), Icon(Icons.movie)),
  const DrawerItem("资源管理", Icon(Icons.group_outlined), Icon(Icons.group)),
];

List<DrawerItem> vipDestinations = <DrawerItem>[
  const DrawerItem("兑换码", Icon(Icons.qr_code_scanner), Icon(Icons.qr_code)),
];

List<DrawerItem> permissionDestinations = <DrawerItem>[
  DrawerItem(LocaleKeys.drawer_permission_page.tr, const Icon(Icons.find_in_page_outlined), const Icon(Icons.find_in_page)),
  DrawerItem(LocaleKeys.drawer_permission_role.tr, const Icon(Icons.verified_user_outlined), const Icon(Icons.verified_user))
];

List<DrawerItem> systemDestinations = <DrawerItem>[
  DrawerItem(LocaleKeys.drawer_scheduled_tasks.tr, const Icon(Icons.schedule_outlined), const Icon(Icons.schedule)),
  DrawerItem(LocaleKeys.drawer_theme_setting.tr, const Icon(Icons.dark_mode_outlined), const Icon(Icons.dark_mode)),
  DrawerItem(LocaleKeys.drawer_locales_setting.tr, const Icon(Icons.translate_outlined), const Icon(Icons.translate))
];

class SideMenu extends StatelessWidget {
  const SideMenu({
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    HomeController homeController = Get.find();
    return Obx(
      () => NavigationDrawer(
        onDestinationSelected: homeController.handleScreenChanged,
        selectedIndex: homeController.screenIndex.value,
        children: <Widget>[
          Padding(
            padding: const EdgeInsets.fromLTRB(28, 16, 16, 10),
            child: Row(
              children: [const Icon(Icons.abc, size: defaultPadding * 4), Text(appName, style: Theme.of(context).textTheme.titleLarge)],
            ),
          ),
          NavigationDrawerDestination(
            label: Text(LocaleKeys.drawer_home.tr),
            icon: const Icon(Icons.widgets),
            selectedIcon: const Icon(Icons.widgets_outlined),
          ),
          Padding(
            padding: const EdgeInsets.fromLTRB(28, 16, 16, 10),
            child: Text(
              LocaleKeys.drawer_user_title.tr,
              style: Theme.of(context).textTheme.titleSmall,
            ),
          ),
          ...userDestinations.map(
            (DrawerItem destination) {
              return NavigationDrawerDestination(
                label: Text(destination.label),
                icon: destination.icon,
                selectedIcon: destination.selectedIcon,
              );
            },
          ),
          const Divider(indent: 28, endIndent: 28),
          Padding(
            padding: const EdgeInsets.fromLTRB(28, 16, 16, 10),
            child: Text(
              "资源",
              style: Theme.of(context).textTheme.titleSmall,
            ),
          ),
          ...sourceDestinations.map(
            (DrawerItem destination) {
              return NavigationDrawerDestination(
                label: Text(destination.label),
                icon: destination.icon,
                selectedIcon: destination.selectedIcon,
              );
            },
          ),
          const Divider(indent: 28, endIndent: 28),
          Padding(
            padding: const EdgeInsets.fromLTRB(28, 16, 16, 10),
            child: Text(
              "会员",
              style: Theme.of(context).textTheme.titleSmall,
            ),
          ),
          ...vipDestinations.map(
            (DrawerItem destination) {
              return NavigationDrawerDestination(
                label: Text(destination.label),
                icon: destination.icon,
                selectedIcon: destination.selectedIcon,
              );
            },
          ),
          const Divider(indent: 28, endIndent: 28),
          Padding(
            padding: const EdgeInsets.fromLTRB(28, 16, 16, 10),
            child: Text(
              LocaleKeys.drawer_permission.tr,
              style: Theme.of(context).textTheme.titleSmall,
            ),
          ),
          ...permissionDestinations.map(
            (DrawerItem destination) {
              return NavigationDrawerDestination(
                label: Text(destination.label),
                icon: destination.icon,
                selectedIcon: destination.selectedIcon,
              );
            },
          ),
          const Divider(indent: 28, endIndent: 28),
          Padding(
            padding: const EdgeInsets.fromLTRB(28, 16, 16, 10),
            child: Text(
              LocaleKeys.drawer_system_setting.tr,
              style: Theme.of(context).textTheme.titleSmall,
            ),
          ),
          ...systemDestinations.map(
            (DrawerItem destination) {
              return NavigationDrawerDestination(
                label: Text(destination.label),
                icon: destination.icon,
                selectedIcon: destination.selectedIcon,
              );
            },
          ),
        ],
      ),
    );
  }
}
