import 'dart:collection';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lsenglish_admin/app/modules/dashboard/views/dashboard_view.dart';
import 'package:lsenglish_admin/app/modules/category/controllers/category_controller.dart';
import 'package:lsenglish_admin/app/modules/category/views/category_view.dart';
import 'package:lsenglish_admin/app/modules/promotion_code/controllers/promotion_code_controller.dart';
import 'package:lsenglish_admin/app/modules/promotion_code/views/promotion_code_view.dart';
import 'package:lsenglish_admin/app/modules/resource/controllers/resource_controller.dart';
import 'package:lsenglish_admin/app/modules/resource/views/resource_view.dart';
import 'package:lsenglish_admin/app/modules/series/controllers/series_controller.dart';
import 'package:lsenglish_admin/app/modules/series/views/series_view.dart';
import 'package:lsenglish_admin/app/modules/users/controllers/users_controller.dart';
import 'package:lsenglish_admin/app/modules/users/views/users_view.dart';

class PanelConfig {
  final Widget Function() viewBuilder;
  final Function()? controllerInitializer;

  PanelConfig({required this.viewBuilder, this.controllerInitializer});
}

class HomeController extends GetxController with GetSingleTickerProviderStateMixin {
  final GlobalKey<ScaffoldState> homeDrawerKey = GlobalKey();

  var screenIndex = 0.obs;

  final Map<int, Widget> panelList = HashMap();

  List<PanelConfig> panelConfigs = [
    PanelConfig(viewBuilder: () => const DashboardView()),
    PanelConfig(viewBuilder: () => const UsersView(), controllerInitializer: () => Get.put(UsersController(), permanent: true)),
    PanelConfig(viewBuilder: () => const CategoryView(), controllerInitializer: () => Get.put(CategoryController(), permanent: true)),
    PanelConfig(viewBuilder: () => const SeriesView(), controllerInitializer: () => Get.put(SeriesController(), permanent: true)),
    PanelConfig(viewBuilder: () => const ResourceView(), controllerInitializer: () => Get.put(ResourceController(), permanent: true)),
    PanelConfig(viewBuilder: () => const PromotionCodeView(), controllerInitializer: () => Get.put(PromotionCodeController(), permanent: true)),
    PanelConfig(viewBuilder: () => const DashboardView()),
  ];

  void openDrawer() {
    homeDrawerKey.currentState?.openDrawer();
  }

  void handleScreenChanged(int selectedScreen) {
    screenIndex.value = selectedScreen;
    homeDrawerKey.currentState?.closeDrawer();
  }

  Widget getPanelWidget() {
    if (panelList.containsKey(screenIndex.value)) {
      return panelList[screenIndex.value] ?? Container();
    } else {
      Widget target = _getPanelWidgetByIndex(screenIndex.value);
      panelList[screenIndex.value] = target;
      return target;
    }
  }

  Widget _getPanelWidgetByIndex(int index) {
    if (index < 0 || index >= panelConfigs.length) {
      return const Text("Empty");
    }
    var config = panelConfigs[index];

    // 初始化控制器（如果有）
    config.controllerInitializer?.call();

    // 创建并返回视图
    return config.viewBuilder();
  }
}
